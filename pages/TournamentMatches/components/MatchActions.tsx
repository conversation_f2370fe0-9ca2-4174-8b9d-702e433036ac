import React from "react";
import { Pressable, View, Text } from "react-native";
import { Match } from "../../../types/matches";
import { ChevronRightIcon, Icon } from "@/components/ui/icon";
import { Edit2Icon, Trash2Icon, PlayIcon, BanIcon } from "lucide-react-native";
import SlideButton from "@/components/k-components/SlideButton";
import { Divider } from "@/components/ui/divider";

interface MatchActionsProps {
  match: Match;
}

const SettingIcon = ({
  onPress,
  icon,
  color,
}: {
  onPress: () => void;
  icon: any;
  color: string;
}) => {
  return (
    <Pressable
      onPress={onPress}
      className={`p-2 border border-${color} rounded-full`}
    >
      <Icon as={icon} size="sm" className={`text-${color}`} />
    </Pressable>
  );
};

const MatchActions: React.FC<MatchActionsProps> = ({ match }) => {
  const handleStartMatch = () => {
    // TODO: Implement start match logic
    console.log("Starting match:", match.id);
  };

  return (
    <View className="flex">
      <View className="rounded-xl bg-white border border-gray-200 shadow-md py-3">
        <View className="flex-row justify-between items-center mb-3 px-4">
          <Text className="text-sm font-urbanistExtraBold tracking-widest text-gray-500 self-center">
            MATCH ACTIONS
          </Text>
          <View className="flex-row justify-end gap-3">
            <SettingIcon onPress={() => {}} icon={Edit2Icon} color="gray-400" />
            <SettingIcon onPress={() => {}} icon={Trash2Icon} color="red-500" />
          </View>
        </View>
        <View className="border-t border-gray-300 border-dashed" />
        <View className="px-4">
          <Pressable className="flex-row justify-between items-center">
            <View className="flex-row items-center justify-center gap-2">
              <Icon as={BanIcon} size="lg" className="text-typography-700" />
              <Text className="text-sm font-urbanistSemiBold text-typography-700 my-2 mb-7">
                Cancel match
              </Text>
            </View>
            <Icon
              as={ChevronRightIcon}
              size="lg"
              className="text-typography-700"
            />
          </Pressable>
          <Divider className="w-ull items-center self-center" />
        </View>
      </View>

      {/* Slide Button for Start Match */}
      <View className="mt-4">
        <SlideButton
          onSlideComplete={handleStartMatch}
          text="Slide to Start Match"
          backgroundColor="bg-primary-0"
          textColor="text-white"
          width="auto"
          height={52}
        />
      </View>
    </View>
  );
};

export default MatchActions;
